# DividerWithTextWidget

Widget مخصص لإنشاء صف يحتوي على خطين مع نص في المنتصف.

## الاستخدام الأساسي

```dart
const DividerWithTextWidget()
```

هذا سينشئ صف مع:
- خط بلون التيل على اليسار
- نص "Continue with" في المنتصف
- خط بلون التيل على اليمين

## الخصائص المتاحة

| الخاصية | النوع | القيمة الافتراضية | الوصف |
|---------|------|-----------------|-------|
| `text` | `String` | `"Continue with"` | النص الذي يظهر بين الخطين |
| `dividerColor` | `Color?` | `context.appColors.teal` | لون الخطين |
| `textStyle` | `TextStyle?` | `AppStyles.textStyle16Teal(context)` | نمط النص |
| `dividerThickness` | `double` | `1.0` | سمك الخطين |
| `spacing` | `double` | `16.0` | المسافة بين النص والخطين |

## أمثلة الاستخدام

### مع نص مخصص
```dart
const DividerWithTextWidget(
  text: "أو تسجيل الدخول بـ",
)
```

### مع لون مخصص
```dart
DividerWithTextWidget(
  text: "Continue with",
  dividerColor: context.appColors.grey,
  textStyle: AppStyles.textStyle16Black(context).copyWith(
    color: context.appColors.grey,
  ),
)
```

### مع خط أكثر سمكاً
```dart
const DividerWithTextWidget(
  text: "Continue with",
  dividerThickness: 2.0,
)
```

### مع مسافات أكبر
```dart
const DividerWithTextWidget(
  text: "Continue with",
  spacing: 30.0,
)
```

## مثال كامل

يمكنك رؤية مثال كامل في ملف `divider_with_text_example.dart` الذي يوضح جميع الاستخدامات المختلفة للـ widget.

## الاستيراد

```dart
import 'package:fasila/core/widgets/divider_with_text_widget.dart';
```
