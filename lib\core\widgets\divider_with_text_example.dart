import 'package:fasila/core/theme/colors.dart';
import 'package:fasila/core/theme/styles.dart';
import 'package:fasila/core/widgets/divider_with_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// مثال على استخدام DividerWithTextWidget بطرق مختلفة
class DividerWithTextExample extends StatelessWidget {
  const DividerWithTextExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Divider With Text Examples'),
      ),
      body: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // المثال الأساسي
            Text(
              'المثال الأساسي:',
              style: AppStyles.textStyle18(context).copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const DividerWithTextWidget(),
            
            const SizedBox(height: 30),
            
            // مثال مع نص مخصص
            Text(
              'مع نص مخصص:',
              style: AppStyles.textStyle18(context).copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const DividerWithTextWidget(
              text: "أو تسجيل الدخول بـ",
            ),
            
            const SizedBox(height: 30),
            
            // مثال مع لون مخصص
            Text(
              'مع لون مخصص:',
              style: AppStyles.textStyle18(context).copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            DividerWithTextWidget(
              text: "Continue with",
              dividerColor: context.appColors.grey,
              textStyle: AppStyles.textStyle16Black(context).copyWith(
                color: context.appColors.grey,
              ),
            ),
            
            const SizedBox(height: 30),
            
            // مثال مع خط أكثر سمكاً
            Text(
              'مع خط أكثر سمكاً:',
              style: AppStyles.textStyle18(context).copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const DividerWithTextWidget(
              text: "Continue with",
              dividerThickness: 2.0,
            ),
            
            const SizedBox(height: 30),
            
            // مثال مع مسافات أكبر
            Text(
              'مع مسافات أكبر:',
              style: AppStyles.textStyle18(context).copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const DividerWithTextWidget(
              text: "Continue with",
              spacing: 30.0,
            ),
            
            const Spacer(),
            
            // ملاحظة للمطور
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: context.appColors.offWhite,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'يمكنك استخدام هذا الـ Widget في أي مكان تحتاج فيه لخطوط مع نص في المنتصف، مثل صفحات تسجيل الدخول أو التسجيل.',
                style: AppStyles.textStyle14(context),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
