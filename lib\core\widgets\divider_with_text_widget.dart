import 'package:fasila/core/theme/colors.dart';
import 'package:fasila/core/theme/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DividerWithTextWidget extends StatelessWidget {
  const DividerWithTextWidget({
    super.key,
    this.text = "Or Continue with",
    this.dividerColor,
    this.textStyle,
    this.dividerThickness = 1.0,
    this.spacing = 16.0,
  });

  final String text;
  final Color? dividerColor;
  final TextStyle? textStyle;
  final double dividerThickness;
  final double spacing;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // الخط الأول
        Expanded(
          child: Divider(
            color: dividerColor ?? context.appColors.teal,
            thickness: dividerThickness,
            height: 1,
          ),
        ),

        // المسافة قبل النص
        SizedBox(width: spacing.w),

        // النص
        Text(text, style: textStyle ?? AppStyles.textStyle16Teal(context)),

        // المسافة بعد النص
        SizedBox(width: spacing.w),

        // الخط الثاني
        Expanded(
          child: Divider(
            color: dividerColor ?? context.appColors.teal,
            thickness: dividerThickness,
            height: 1,
          ),
        ),
      ],
    );
  }
}
