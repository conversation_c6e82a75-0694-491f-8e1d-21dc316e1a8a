import 'package:fasila/core/theme/colors.dart';
import 'package:fasila/core/theme/styles.dart';
import 'package:fasila/core/widgets/custom_text_field_widget.dart';
import 'package:fasila/core/widgets/custom_textformfield_widget.dart';
import 'package:fasila/core/widgets/divider_with_text_widget.dart';
import 'package:flutter/material.dart';

class LoginViewBody extends StatelessWidget {
  const LoginViewBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 70, left: 20, right: 20),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text('Login here', style: AppStyles.textStyle24(context)),
            const SizedBox(height: 10),
            Text(
              'Welcome back you’ve been missed!',
              style: AppStyles.textStyle12<PERSON>rey(
                context,
              ).copyWith(color: context.appColors.black),
            ),
            const SizedBox(height: 30),
            CustomTextFieldWidget(hintText: 'Email'),
            const SizedBox(height: 20),
            CustomTextFormFieldWidget(hintText: 'Password'),
            const SizedBox(height: 25),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Forgot Password?',
                  style: AppStyles.textStyle12Grey(context),
                ),
                Text(
                  'Create New Account',
                  style: AppStyles.textStyle12Grey(context),
                ),
              ],
            ),
            const SizedBox(height: 30),

            // استخدام الـ widget الجديد
            const DividerWithTextWidget(text: "Continue with"),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
